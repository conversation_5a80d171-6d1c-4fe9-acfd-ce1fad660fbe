package api

import (
	"hospital-management/internal/model"
	"hospital-management/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type DepartmentHandler struct {
	departmentService *service.DepartmentService
}

func NewDepartmentHandler(departmentService *service.DepartmentService) *DepartmentHandler {
	return &DepartmentHandler{departmentService: departmentService}
}

func (h *DepartmentHandler) List(c *gin.Context) {
	departments, err := h.departmentService.List()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取部门列表失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    departments,
	})
}

func (h *DepartmentHandler) GetTree(c *gin.Context) {
	tree, err := h.departmentService.GetTree()
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取部门树失败",
			"data":    nil,
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    tree,
	})
}

func (h *DepartmentHandler) Create(c *gin.Context) {
	var dept model.Department
	if err := c.ShouldBindJSON(&dept); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	if err := h.departmentService.Create(&dept); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建部门失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    dept,
	})
}

func (h *DepartmentHandler) GetByID(c *gin.Context) {
	id := c.Param("id")

	dept, err := h.departmentService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40004,
			"message": "部门不存在",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "获取成功",
		"data":    dept,
	})
}

func (h *DepartmentHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID不能为空",
			"data":    nil,
		})
		return
	}

	var dept model.Department
	if err := c.ShouldBindJSON(&dept); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	// 解析UUID
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	dept.ID = id
	if err := h.departmentService.Update(&dept); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新部门失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    dept,
	})
}

func (h *DepartmentHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID不能为空",
			"data":    nil,
		})
		return
	}

	if err := h.departmentService.Delete(idStr); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除部门失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
		"data":    nil,
	})
}
