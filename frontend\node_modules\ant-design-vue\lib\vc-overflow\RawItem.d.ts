declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    component: import("vue-types").VueTypeValidableDef<any>;
    title: import("vue-types").VueTypeValidableDef<any>;
    id: StringConstructor;
    onMouseenter: {
        type: FunctionConstructor;
    };
    onMouseleave: {
        type: FunctionConstructor;
    };
    onClick: {
        type: FunctionConstructor;
    };
    onKeydown: {
        type: FunctionConstructor;
    };
    onFocus: {
        type: FunctionConstructor;
    };
    role: StringConstructor;
    tabindex: NumberConstructor;
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    component: import("vue-types").VueTypeValidableDef<any>;
    title: import("vue-types").VueTypeValidableDef<any>;
    id: StringConstructor;
    onMouseenter: {
        type: FunctionConstructor;
    };
    onMouseleave: {
        type: FunctionConstructor;
    };
    onClick: {
        type: FunctionConstructor;
    };
    onKeydown: {
        type: FunctionConstructor;
    };
    onFocus: {
        type: FunctionConstructor;
    };
    role: StringConstructor;
    tabindex: NumberConstructor;
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
