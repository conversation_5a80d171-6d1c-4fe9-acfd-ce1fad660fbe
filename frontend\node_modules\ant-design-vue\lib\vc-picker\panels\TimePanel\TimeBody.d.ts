import type { GenerateConfig } from '../../generate';
import type { Locale, OnSelect } from '../../interface';
import type { SharedTimeProps } from '.';
import type { Ref } from 'vue';
export type BodyOperationRef = {
    onUpDown: (diff: number) => void;
};
export type TimeBodyProps<DateType> = {
    prefixCls: string;
    locale: Locale;
    generateConfig: GenerateConfig<DateType>;
    value?: DateType | null;
    onSelect: OnSelect<DateType>;
    activeColumnIndex: number;
    operationRef: Ref<BodyOperationRef | undefined>;
} & SharedTimeProps<DateType>;
declare const TimeBody: import("vue").DefineComponent<{
    value?: any;
    onSelect?: any;
    prefixCls?: any;
    generateConfig?: any;
    operationRef?: any;
    hideDisabledOptions?: any;
    activeColumnIndex?: any;
    showHour?: any;
    showMinute?: any;
    showSecond?: any;
    use12Hours?: any;
    hourStep?: any;
    minuteStep?: any;
    secondStep?: any;
    disabledHours?: any;
    disabledMinutes?: any;
    disabledSeconds?: any;
    disabledTime?: any;
}, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{
    value?: any;
    onSelect?: any;
    prefixCls?: any;
    generateConfig?: any;
    operationRef?: any;
    hideDisabledOptions?: any;
    activeColumnIndex?: any;
    showHour?: any;
    showMinute?: any;
    showSecond?: any;
    use12Hours?: any;
    hourStep?: any;
    minuteStep?: any;
    secondStep?: any;
    disabledHours?: any;
    disabledMinutes?: any;
    disabledSeconds?: any;
    disabledTime?: any;
}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default TimeBody;
