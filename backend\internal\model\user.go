package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User 用户表 (tbl_users)
type User struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	UserName     string     `json:"user_name" gorm:"size:100;not null;comment:用户姓名"`
	EmployeeID   string     `json:"employee_id" gorm:"uniqueIndex;size:50;comment:工号"`
	Email        string     `json:"email" gorm:"uniqueIndex;size:255;comment:邮箱"`
	PhoneNumber  string     `json:"phone_number" gorm:"column:phone_number;uniqueIndex;size:20;comment:手机号"`
	PasswordHash string     `json:"-" gorm:"column:password_hash;size:255;not null;comment:密码"`
	JobTitle     string     `json:"job_title" gorm:"column:job_title;size:100;comment:职位"`
	DepartmentID uuid.UUID  `json:"department_id" gorm:"type:uuid;not null;comment:部门ID"`
	IsActive     bool       `json:"is_active" gorm:"column:is_active;not null;default:true;comment:是否启用"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy    *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy    *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Department Department  `json:"department" gorm:"foreignKey:DepartmentID"`
	Roles      []Role      `json:"roles" gorm:"many2many:tbl_user_roles;"`
}

// Department 部门表 (tbl_departments)
type Department struct {
	ID          uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	Name        string     `json:"name" gorm:"size:100;not null;comment:部门名称"`
	Code        string     `json:"code" gorm:"uniqueIndex;size:50;not null;comment:部门编码"`
	ParentID    *uuid.UUID `json:"parent_id" gorm:"type:uuid;comment:上级部门ID"`
	Level       int        `json:"level" gorm:"default:1;comment:层级"`
	SortOrder   int        `json:"sort_order" gorm:"default:0;comment:排序"`
	Description string     `json:"description" gorm:"type:text;comment:描述"`
	Status      int        `json:"status" gorm:"default:1;comment:状态 1-启用 0-禁用"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Parent   *Department  `json:"parent" gorm:"foreignKey:ParentID"`
	Children []Department `json:"children" gorm:"foreignKey:ParentID"`
}

// Role 角色表 (tbl_roles)
type Role struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	RoleName    string    `json:"role_name" gorm:"size:100;not null;uniqueIndex;comment:角色名称"`
	RoleCode    string    `json:"role_code" gorm:"size:50;not null;uniqueIndex;comment:角色编码"`
	Permissions string    `json:"permissions" gorm:"type:jsonb;comment:权限集合JSON"`
	Description string    `json:"description" gorm:"type:text;comment:角色描述"`
	Status      int       `json:"status" gorm:"default:1;comment:状态 1-启用 0-禁用"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`
}

// UserRole 用户角色关联表 (tbl_user_roles)
type UserRole struct {
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;primaryKey"`
	RoleID uuid.UUID `json:"role_id" gorm:"type:uuid;primaryKey"`
}

// File 文件管理表 (tbl_files)
type File struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	FileName    string    `json:"file_name" gorm:"size:255;not null;comment:原始文件名"`
	FilePath    string    `json:"file_path" gorm:"size:500;not null;comment:文件存储路径"`
	FileSize    int64     `json:"file_size" gorm:"comment:文件大小(字节)"`
	MimeType    string    `json:"mime_type" gorm:"size:100;comment:文件MIME类型"`
	StorageType string    `json:"storage_type" gorm:"size:20;not null;comment:存储类型 LOCAL/OSS"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`
}

// TableName 设置表名
func (User) TableName() string {
	return "tbl_users"
}

func (Department) TableName() string {
	return "tbl_departments"
}

func (Role) TableName() string {
	return "tbl_roles"
}

func (UserRole) TableName() string {
	return "tbl_user_roles"
}

func (File) TableName() string {
	return "tbl_files"
}
