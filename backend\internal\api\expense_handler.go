package api

import (
	"hospital-management/internal/model"
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type ExpenseHandler struct {
	expenseService *service.ExpenseService
}

func NewExpenseHandler(expenseService *service.ExpenseService) *ExpenseHandler {
	return &ExpenseHandler{expenseService: expenseService}
}

// CreateApplication 创建报销申请
func (h *ExpenseHandler) CreateApplication(c *gin.Context) {
	var reqData struct {
		Title        string `json:"title" binding:"required"`
		DepartmentID string `json:"department_id" binding:"required"`
		Details      []struct {
			BudgetItemID string  `json:"budget_item_id" binding:"required"`
			Description  string  `json:"description" binding:"required"`
			Amount       float64 `json:"amount" binding:"required"`
			ExpenseDate  string  `json:"expense_date" binding:"required"`
			InvoiceInfo  string  `json:"invoice_info"`
		} `json:"details" binding:"required"`
		Attachments []string `json:"attachments"`
	}

	if err := c.ShouldBindJ<PERSON>(&reqData); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	// 转换为service期望的类型
	req := &service.CreateApplicationRequest{
		Title:        reqData.Title,
		DepartmentID: reqData.DepartmentID,
		Attachments:  reqData.Attachments,
	}

	// 转换Details
	for _, detail := range reqData.Details {
		req.Details = append(req.Details, struct {
			BudgetItemID string  `json:"budget_item_id"`
			Description  string  `json:"description"`
			Amount       float64 `json:"amount"`
			ExpenseDate  string  `json:"expense_date"`
			InvoiceInfo  string  `json:"invoice_info"`
		}{
			BudgetItemID: detail.BudgetItemID,
			Description:  detail.Description,
			Amount:       detail.Amount,
			ExpenseDate:  detail.ExpenseDate,
			InvoiceInfo:  detail.InvoiceInfo,
		})
	}

	userID := c.GetString("user_id")
	application, err := h.expenseService.CreateApplication(userID, req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20100,
		"message": "报销单提交成功，已进入审批流程",
		"data": gin.H{
			"id":               application.ID,
			"application_code": application.ApplicationCode,
			"status":           application.Status,
		},
	})
}

// GetApplications 查询报销申请列表
func (h *ExpenseHandler) GetApplications(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	status := c.Query("status")
	startDate := c.Query("startDate")
	endDate := c.Query("endDate")

	userID := c.GetString("user_id")
	applications, total, err := h.expenseService.GetApplicationsByUser(userID, page, pageSize, status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "查询失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "查询成功",
		"data": gin.H{
			"total": total,
			"list":  applications,
		},
	})
}

// GetApplicationByID 根据ID获取报销申请详情
func (h *ExpenseHandler) GetApplicationByID(c *gin.Context) {
	id := c.Param("id")
	if _, err := uuid.Parse(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	application, err := h.expenseService.GetApplicationByID(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40004,
			"message": "报销申请不存在",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "获取成功",
		"data":    application,
	})
}

// UpdateApplication 更新报销申请
func (h *ExpenseHandler) UpdateApplication(c *gin.Context) {
	id := c.Param("id")
	if _, err := uuid.Parse(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	var application model.ExpenseApplication
	if err := c.ShouldBindJSON(&application); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	applicationID, _ := uuid.Parse(id)
	application.ID = applicationID
	if err := h.expenseService.UpdateApplication(&application); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "更新报销申请失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "更新成功",
		"data":    application,
	})
}

// DeleteApplication 删除报销申请
func (h *ExpenseHandler) DeleteApplication(c *gin.Context) {
	id := c.Param("id")
	if _, err := uuid.Parse(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	if err := h.expenseService.DeleteApplication(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "删除报销申请失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "删除成功",
		"data":    nil,
	})
}
