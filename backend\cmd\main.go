package main

import (
	"hospital-management/internal/api"
	"hospital-management/internal/config"
	"hospital-management/internal/repository"
	"hospital-management/internal/service"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 注释掉自动迁移，使用现有的SQL文件创建的表
	// if err := config.AutoMigrate(db); err != nil {
	// 	log.Fatal("Failed to migrate database:", err)
	// }

	// 初始化仓储层
	userRepo := repository.NewUserRepository(db)
	departmentRepo := repository.NewDepartmentRepository(db)
	roleRepo := repository.NewRoleRepository(db)
	budgetRepo := repository.NewBudgetRepository(db)
	expenseRepo := repository.NewExpenseRepository(db)
	approvalRepo := repository.NewApprovalRepository(db)
	receiptRepo := repository.NewReceiptRepository(db)
	purchaseOrderRepo := repository.NewPurchaseOrderRepository(db)
	contractRepo := repository.NewContractRepository(db)
	supplierRepo := repository.NewSupplierRepository(db)
	assetRepo := repository.NewAssetRepository(db)

	// 初始化服务层
	authService := service.NewAuthService(userRepo, cfg.JWT.Secret)
	userService := service.NewUserService(userRepo)
	departmentService := service.NewDepartmentService(departmentRepo)
	roleService := service.NewRoleService(roleRepo)
	budgetService := service.NewBudgetService(budgetRepo)
	expenseService := service.NewExpenseService(expenseRepo, budgetRepo)
	approvalService := service.NewApprovalService(approvalRepo)
	receiptService := service.NewReceiptService(receiptRepo, purchaseOrderRepo)
	contractService := service.NewContractService(contractRepo, supplierRepo)
	assetService := service.NewAssetService(assetRepo)

	// 初始化路由
	router := gin.Default()

	// 设置CORS中间件
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// 注册路由
	api.RegisterRoutes(router, &api.Services{
		Auth:       authService,
		User:       userService,
		Department: departmentService,
		Role:       roleService,
		Budget:     budgetService,
		Expense:    expenseService,
		Approval:   approvalService,
		Receipt:    receiptService,
		Contract:   contractService,
		Asset:      assetService,
	}, cfg.JWT.Secret)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	if err := router.Run(":" + cfg.Server.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
