package main

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	// 创建Gin路由
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "医院内部控制与运营管理系统后端服务运行正常",
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/login", func(c *gin.Context) {
				c.<PERSON>(http.StatusOK, gin.H{
					"success": true,
					"message": "登录成功",
					"data": gin.H{
						"token":    "mock-jwt-token",
						"userInfo": gin.H{
							"id":       "1",
							"username": "admin",
							"realName": "系统管理员",
							"roles":    []string{"admin"},
						},
					},
				})
			})
		}

		// 用户管理
		users := api.Group("/users")
		{
			users.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"items": []gin.H{
							{
								"id":         "1",
								"username":   "admin",
								"real_name":  "系统管理员",
								"email":      "<EMAIL>",
								"phone":      "13800138000",
								"status":     1,
								"created_at": "2024-01-01T00:00:00Z",
							},
							{
								"id":         "2",
								"username":   "doctor1",
								"real_name":  "张医生",
								"email":      "<EMAIL>",
								"phone":      "13800138001",
								"status":     1,
								"created_at": "2024-01-01T00:00:00Z",
							},
						},
						"total":      2,
						"page":       1,
						"page_size":  10,
						"total_page": 1,
					},
				})
			})
		}

		// 部门管理
		departments := api.Group("/departments")
		{
			departments.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": []gin.H{
						{
							"id":          "1",
							"name":        "内科",
							"code":        "NK",
							"description": "内科部门",
							"parent_id":   nil,
							"children":    []gin.H{},
						},
						{
							"id":          "2",
							"name":        "外科",
							"code":        "WK",
							"description": "外科部门",
							"parent_id":   nil,
							"children":    []gin.H{},
						},
					},
				})
			})
		}

		// 预算管理
		budgets := api.Group("/budgets")
		{
			budgets.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"items": []gin.H{
							{
								"id":              "1",
								"budget_name":     "2024年度预算",
								"budget_year":     2024,
								"total_amount":    1000000.00,
								"allocated_amount": 800000.00,
								"used_amount":     600000.00,
								"status":          "ACTIVE",
								"created_at":      "2024-01-01T00:00:00Z",
							},
						},
						"total":      1,
						"page":       1,
						"page_size":  10,
						"total_page": 1,
					},
				})
			})
		}

		// 费用报销
		expenses := api.Group("/expenses")
		{
			expenses.GET("/reports", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"items": []gin.H{
							{
								"id":           "1",
								"title":        "差旅费报销",
								"total_amount": 2500.00,
								"status":       "PENDING",
								"created_at":   "2024-01-15T00:00:00Z",
								"applicant":    gin.H{"real_name": "张三"},
							},
						},
						"total":      1,
						"page":       1,
						"page_size":  10,
						"total_page": 1,
					},
				})
			})
		}

		// 审批管理
		approvals := api.Group("/approvals")
		{
			approvals.GET("/tasks", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": []gin.H{
						{
							"id":            "1",
							"business_type": "EXPENSE_REPORT",
							"business_id":   "1",
							"title":         "差旅费报销审批",
							"status":        "PENDING",
							"created_at":    "2024-01-15T00:00:00Z",
							"applicant":     gin.H{"real_name": "张三"},
						},
					},
				})
			})
		}

		// 入库验收
		receipts := api.Group("/receipts")
		{
			receipts.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"items": []gin.H{
							{
								"id":           "1",
								"receipt_code": "RK20240115001",
								"total_amount": 50000.00,
								"status":       "RECEIVED",
								"created_at":   "2024-01-15T00:00:00Z",
								"supplier":     gin.H{"name": "医疗设备供应商A"},
							},
						},
						"total":      1,
						"page":       1,
						"page_size":  10,
						"total_page": 1,
					},
				})
			})
		}

		// 合同管理
		contracts := api.Group("/contracts")
		{
			contracts.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"items": []gin.H{
							{
								"id":               "1",
								"contract_code":    "HT20240101001",
								"contract_name":    "医疗设备采购合同",
								"total_amount":     500000.00,
								"status":           "ACTIVE",
								"counterparty_name": "医疗设备供应商A",
								"created_at":       "2024-01-01T00:00:00Z",
							},
						},
						"total":      1,
						"page":       1,
						"page_size":  10,
						"total_page": 1,
					},
				})
			})
		}

		// 资产管理
		assets := api.Group("/assets")
		{
			assets.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"items": []gin.H{
							{
								"id":             "1",
								"asset_code":     "ZC20240101001",
								"asset_name":     "CT扫描仪",
								"purchase_price": 800000.00,
								"status":         "NORMAL",
								"created_at":     "2024-01-01T00:00:00Z",
								"category":       gin.H{"category_name": "医疗设备"},
								"department":     gin.H{"name": "放射科"},
							},
						},
						"total":      1,
						"page":       1,
						"page_size":  10,
						"total_page": 1,
					},
				})
			})
		}
	}

	log.Println("🚀 医院内部控制与运营管理系统后端服务启动成功!")
	log.Println("📍 服务地址: http://localhost:7823")
	log.Println("🔍 健康检查: http://localhost:7823/health")
	log.Println("📚 API文档: http://localhost:7823/api/v1")

	// 启动服务器
	if err := r.Run(":7823"); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}
